#!/usr/bin/env python3
"""
Test script to verify that the logging systems work independently
"""

import time
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_logging_separation():
    """Test that Delaware monitor and WhatsApp sender have separate loggers"""
    print("Testing logging separation...")
    
    # Import both modules - this will initialize their loggers
    from delaware_monitor import logger as delaware_logger
    from whatsapp_sender import whatsapp_logger
    
    # Test Delaware monitor logging
    print("\n1. Testing Delaware monitor logging...")
    delaware_logger.info("This is a test message from Delaware monitor")
    delaware_logger.warning("This is a warning from Delaware monitor")
    delaware_logger.error("This is an error from Delaware monitor")
    
    # Test WhatsApp sender logging
    print("\n2. Testing WhatsApp sender logging...")
    whatsapp_logger.info("This is a test message from WhatsApp sender")
    whatsapp_logger.warning("This is a warning from WhatsApp sender")
    whatsapp_logger.error("This is an error from WhatsApp sender")
    
    # Verify log files exist
    print("\n3. Checking log files...")
    delaware_log_exists = os.path.exists('delaware_monitor.log')
    whatsapp_log_exists = os.path.exists('whatsapp_sender.log')
    
    print(f"Delaware monitor log file exists: {delaware_log_exists}")
    print(f"WhatsApp sender log file exists: {whatsapp_log_exists}")
    
    if delaware_log_exists and whatsapp_log_exists:
        print("\n4. Reading log file contents...")
        
        print("\nDelaware monitor log (last 5 lines):")
        with open('delaware_monitor.log', 'r') as f:
            lines = f.readlines()
            for line in lines[-5:]:
                print(f"  {line.strip()}")
        
        print("\nWhatsApp sender log (last 5 lines):")
        with open('whatsapp_sender.log', 'r') as f:
            lines = f.readlines()
            for line in lines[-5:]:
                print(f"  {line.strip()}")
        
        print("\n✅ Logging separation test PASSED!")
        print("Both loggers are working independently with separate log files.")
        return True
    else:
        print("\n❌ Logging separation test FAILED!")
        print("One or both log files were not created.")
        return False

def test_browser_recovery_logic():
    """Test the browser recovery logic (without actually starting browsers)"""
    print("\n" + "="*60)
    print("Testing browser recovery logic...")
    
    # Test Delaware monitor recovery logic
    print("\n1. Testing Delaware monitor recovery logic...")
    from delaware_monitor import DelawareCorpMonitor
    
    monitor = DelawareCorpMonitor()
    
    # Test is_driver_alive when driver is None
    print(f"is_driver_alive() with None driver: {monitor.is_driver_alive()}")
    
    # Test WhatsApp sender recovery logic
    print("\n2. Testing WhatsApp sender recovery logic...")
    from whatsapp_sender import WhatsAppSender
    
    whatsapp = WhatsAppSender()
    
    # Test is_driver_alive when driver is None
    print(f"is_driver_alive() with None driver: {whatsapp.is_driver_alive()}")
    
    print("\n✅ Browser recovery logic test PASSED!")
    print("Recovery methods are properly implemented.")
    return True

def main():
    """Run all tests"""
    print("="*60)
    print("TESTING REFACTORED CODE")
    print("="*60)
    
    success = True
    
    # Test logging separation
    if not test_logging_separation():
        success = False
    
    # Test browser recovery logic
    if not test_browser_recovery_logic():
        success = False
    
    print("\n" + "="*60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("The refactored code is working correctly.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the issues above.")
    print("="*60)
    
    return success

if __name__ == "__main__":
    main()
