import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from winotify import Notification
# Import the WhatsApp sender function
from whatsapp_sender import WhatsAppSender, send_whatsapp_notification

# Configure logging for Delaware monitor
def setup_delaware_logger():
    """Set up a dedicated logger for Delaware monitor"""
    logger = logging.getLogger('delaware_monitor')
    logger.setLevel(logging.INFO)

    # Prevent adding handlers multiple times
    if not logger.handlers:
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # File handler
        file_handler = logging.FileHandler('delaware_monitor.log')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    return logger

# Initialize the logger
logger = setup_delaware_logger()

class DelawareCorpMonitor:
    def __init__(self, check_interval_minutes=10, whatsapp_contact=None):
        self.url = "https://icis.corp.delaware.gov/ecorp2/"
        self.check_interval_minutes = check_interval_minutes
        self.check_interval = check_interval_minutes * 60  # Convert to seconds
        self.driver = None
        self.chromedriver_path = "C:\\Users\\<USER>\\tmp\\chromedriver\\chromedriver.exe"
        self.emergency_selector = "div.emergency"
        self.site_was_down = False
        self.whatsapp_contact = whatsapp_contact  # Contact name or phone number for WhatsApp
        
    def setup_driver(self):
        """Initialize Chrome WebDriver with optimal settings"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-background-networking")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-breakpad")
        chrome_options.add_argument("--disable-component-update")
        chrome_options.add_argument("--disable-domain-reliability")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-features=TranslateUI")
        chrome_options.add_argument("--disable-hang-monitor")
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-prompt-on-repost")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--force-color-profile=srgb")
        chrome_options.add_argument("--metrics-recording-only")
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--password-store=basic")
        chrome_options.add_argument("--use-mock-keychain")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

        try:
            # Clean up existing driver if it exists
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None

            # You may need to specify the path to chromedriver.exe
            if self.chromedriver_path:
                service = Service(self.chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            logger.info("WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize WebDriver: {e}")
            raise

    def restart_driver(self):
        """Restart the WebDriver after a crash"""
        logger.warning("Attempting to restart WebDriver...")
        try:
            self.setup_driver()
            logger.info("WebDriver restarted successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to restart WebDriver: {e}")
            return False

    def is_driver_alive(self):
        """Check if the WebDriver is still alive and responsive"""
        try:
            if self.driver is None:
                return False
            # Try to get the current URL to test if driver is responsive
            _ = self.driver.current_url
            return True
        except Exception:
            return False
    
    def check_site_status(self):
        """Check if the site is up or down"""
        # Check if driver is alive, restart if needed
        if not self.is_driver_alive():
            logger.warning("WebDriver appears to be dead, attempting restart...")
            if not self.restart_driver():
                logger.error("Failed to restart WebDriver, treating as site down")
                self.site_was_down = True
                return False

        try:
            logger.info(f"Checking site status: {self.url}")
            self.driver.get(self.url)

            # Wait for page to load completely
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Small additional wait for dynamic content
            time.sleep(3)

            # Look for the emergency div
            emergency_elements = self.driver.find_elements(By.CSS_SELECTOR, self.emergency_selector)

            if emergency_elements:
                # Site is down - emergency message is present
                logger.warning("Site is DOWN - Emergency message detected")
                self.site_was_down = True
                return False
            else:
                # Site appears to be up - no emergency message
                logger.info("Site is UP - No emergency message found")
                return True

        except TimeoutException:
            logger.error("Page load timeout - treating as site down")
            self.site_was_down = True
            return False
        except WebDriverException as e:
            logger.error(f"WebDriver error: {e}")
            # Try to restart driver on WebDriver errors
            if not self.restart_driver():
                logger.error("Failed to restart WebDriver after error")
            self.site_was_down = True
            return False
        except Exception as e:
            logger.error(f"Unexpected error during site check: {e}")
            self.site_was_down = True
            return False
    
    def send_notification(self, title, message, whatsapp_sender=None):
        """Send Windows notification and optionally WhatsApp message"""
        try:
            # Send Windows notification
            toast = Notification(
                app_id="Delaware Corp Monitor",
                title=title,
                msg=message,
                duration="long",
                icon="https://corp.delaware.gov/wp-content/themes/delaware/images/favicon.ico"
            )
            toast.show()
            logger.info(f"Windows notification sent: {title}")

            # Send WhatsApp message if contact is configured
            if self.whatsapp_contact and "BACK UP" in title:
                whatsapp_message = f"🚨 {title}\n\n{message}\n\nTime to register your business!\n\n{self.url}"
                try:
                    # Uncomment the next line when you have whatsapp_sender.py ready
                    success = whatsapp_sender.send_whatsapp_notification(self.whatsapp_contact, whatsapp_message)
                    if success:
                        logger.info(f"WhatsApp message sent to {self.whatsapp_contact}")
                    else:
                        logger.warning("Failed to send WhatsApp message")
                    # logger.info("WhatsApp integration ready")
                except Exception as e:
                    logger.error(f"WhatsApp notification error: {e}")

        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")
    
    def run_monitor(self, whatsapp_sender=None):
        """Main monitoring loop"""
        logger.info("Starting Delaware Corporation site monitor")
        logger.info(f"Check interval: {self.check_interval_minutes} minutes")
        
        try:
            self.setup_driver()
            
            # Initial notification
            self.send_notification(
                "Monitor Started", 
                f"Monitoring Delaware Corp site every {self.check_interval_minutes} minutes"
            )
            
            while True:
                try:
                    is_up = self.check_site_status()
                    
                    if is_up and self.site_was_down:
                        # Site is back up after being down
                        self.send_notification(
                            "🟢 Site is BACK UP!",
                            "Delaware Division of Corporations site is now accessible for business registration!",
                            whatsapp_sender
                        )
                        self.site_was_down = False
                        
                    elif is_up and not self.site_was_down:
                        # Site is up and was up before
                        logger.info("Site remains UP")

                    elif not is_up:
                        # Site is down
                        if not self.site_was_down:
                            self.send_notification(
                                "🔴 Site is DOWN",
                                "Delaware Division of Corporations site is experiencing high traffic"
                            )

                    logger.info(f"Next check in {self.check_interval_minutes} minutes...")
                    time.sleep(self.check_interval)

                except KeyboardInterrupt:
                    logger.info("Monitor stopped by user")
                    break
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {e}")
                    # Try to restart driver if there was an error
                    if not self.is_driver_alive():
                        logger.warning("Driver appears dead after error, attempting restart...")
                        self.restart_driver()
                    time.sleep(60)  # Wait 1 minute before retrying
                    
        except Exception as e:
            logger.error(f"Failed to start monitor: {e}")
        finally:
            self.cleanup()

def main():
    # Configuration
    CHECK_INTERVAL_MINUTES = 5
    WHATSAPP_CONTACT = "Team Pedro"  # Set to contact name or phone number like "+1234567890"
    
    monitor = DelawareCorpMonitor(
        check_interval_minutes=CHECK_INTERVAL_MINUTES,
        whatsapp_contact=WHATSAPP_CONTACT
    )
    
    whatsapp = WhatsAppSender()
    whatsapp.start_browser()

    try:
        monitor.run_monitor(whatsapp)
    except KeyboardInterrupt:
        print("\nMonitor stopped by user")
    except Exception as e:
        print(f"Monitor failed: {e}")

if __name__ == "__main__":
    main()