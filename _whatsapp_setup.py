#!/usr/bin/env python3
"""
WhatsApp Web Setup Script
This script helps you set up WhatsApp Web login for the first time.
Run this once to scan the QR code and establish the session.
"""

import time
import logging
from whatsapp_sender import WhatsAppSender

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def setup_whatsapp():
    """Initial setup for WhatsApp Web - scan QR code and save session"""
    print("="*60)
    print("         WHATSAPP WEB SETUP")
    print("="*60)
    print("This script will:")
    print("1. Open a Chrome browser window")
    print("2. Navigate to WhatsApp Web")
    print("3. Display a QR code for you to scan")
    print("4. Save your login session for future use")
    print("="*60)
    
    input("Press Enter to continue...")
    
    whatsapp = None
    try:
        print("\n🚀 Starting WhatsApp Web setup...")
        
        # Initialize the WhatsApp sender
        whatsapp = WhatsAppSender()
        whatsapp.setup_driver()
        
        print("✅ Browser opened successfully")
        print("🌐 Navigating to WhatsApp Web...")
        
        # Login to WhatsApp (this will handle QR code scanning)
        if whatsapp.login_to_whatsapp():
            print("✅ Successfully logged in to WhatsApp Web!")
            print("💾 Your session has been saved.")
            print("\n" + "="*60)
            print("SUCCESS! Your WhatsApp Web is now set up.")
            print("You can now:")
            print("1. Run the test script to send a test message")
            print("2. Enable WhatsApp notifications in your monitor")
            print("3. Close this browser window (session is saved)")
            print("="*60)
            
            # Keep browser open for user to explore
            input("\nPress Enter to close the browser...")
            return True
        else:
            print("❌ Failed to set up WhatsApp Web")
            print("Please try again and make sure to scan the QR code with your phone")
            return False
            
    except KeyboardInterrupt:
        print("\n⏹️  Setup cancelled by user")
        return False
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        logging.error(f"WhatsApp setup error: {e}")
        return False
    finally:
        if whatsapp:
            print("🧹 Cleaning up...")
            whatsapp.close()  # Completely close for setup

if __name__ == "__main__":
    try:
        success = setup_whatsapp()
        if success:
            print("\n🎉 Setup completed successfully!")
            print("Next step: Run 'python whatsapp_test.py' to send a test message")
        else:
            print("\n😞 Setup failed. Please try again.")
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print("Please check the logs and try again.")