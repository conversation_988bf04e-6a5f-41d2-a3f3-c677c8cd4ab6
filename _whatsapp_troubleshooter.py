#!/usr/bin/env python3
"""
WhatsApp Troubleshooting Script
This script helps diagnose and fix common WhatsApp Web integration issues.
"""

import os
import shutil
import logging
from pathlib import Path
from whatsapp_sender import WhatsAppSender

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def check_chrome_profile():
    """Check if Chrome profile directory exists and show info"""
    profile_path = Path("./whatsapp_chrome_data")
    
    print("="*60)
    print("         CHROME PROFILE CHECK")
    print("="*60)
    
    if profile_path.exists():
        print("✅ WhatsApp Chrome profile directory found")
        print(f"📁 Location: {profile_path.absolute()}")
        
        # Check size
        total_size = sum(f.stat().st_size for f in profile_path.glob('**/*') if f.is_file())
        size_mb = total_size / (1024 * 1024)
        print(f"📊 Profile size: {size_mb:.1f} MB")
        
        # List main directories
        subdirs = [d.name for d in profile_path.iterdir() if d.is_dir()]
        if subdirs:
            print(f"📂 Contains: {', '.join(subdirs[:5])}")
            if len(subdirs) > 5:
                print(f"    ... and {len(subdirs) - 5} more directories")
        
        return True
    else:
        print("❌ WhatsApp Chrome profile not found")
        print("💡 This means you haven't run the setup yet")
        return False

def clear_chrome_profile():
    """Clear the Chrome profile to start fresh"""
    profile_path = Path("./whatsapp_chrome_data")
    
    if profile_path.exists():
        try:
            shutil.rmtree(profile_path)
            print("✅ Chrome profile cleared successfully")
            print("💡 You'll need to scan the QR code again next time")
            return True
        except Exception as e:
            print(f"❌ Failed to clear profile: {e}")
            return False
    else:
        print("ℹ️  No Chrome profile to clear")
        return True

def test_browser_connectivity():
    """Test if we can open a browser and connect to WhatsApp Web"""
    print("="*60)
    print("         BROWSER CONNECTIVITY TEST")
    print("="*60)
    
    whatsapp = None
    try:
        print("🚀 Testing browser startup...")
        whatsapp = WhatsAppSender()
        whatsapp.setup_driver()
        print("✅ Browser started successfully")
        
        print("🌐 Testing WhatsApp Web connectivity...")
        whatsapp.driver.get("https://web.whatsapp.com")
        print("✅ WhatsApp Web loaded successfully")
        
        # Check if we can find key elements
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        try:
            # Look for either QR code or chat list
            WebDriverWait(whatsapp.driver, 10).until(
                lambda driver: driver.find_elements(By.CSS_SELECTOR, '[data-testid="qr-code"]') or
                              driver.find_elements(By.CSS_SELECTOR, '[data-testid="chat-list"]')
            )
            print("✅ WhatsApp Web interface detected")
            return True
        except:
            print("⚠️  WhatsApp Web loaded but interface not recognized")
            return False
            
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        return False
    finally:
        if whatsapp:
            whatsapp.close()

def show_system_info():
    """Show system and environment information"""
    print("="*60)
    print("         SYSTEM INFORMATION")
    print("="*60)
    
    import sys
    import platform
    
    print(f"🐍 Python version: {sys.version}")
    print(f"💻 Operating System: {platform.system()} {platform.release()}")
    print(f"🏗️  Architecture: {platform.architecture()[0]}")
    
    # Check for required packages
    try:
        import selenium
        print(f"🕷️  Selenium version: {selenium.__version__}")
    except ImportError:
        print("❌ Selenium not installed")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        print("✅ Chrome WebDriver available")
    except ImportError as e:
        print(f"❌ Chrome WebDriver issue: {e}")

def main_menu():
    """Main troubleshooting menu"""
    while True:
        print("\n" + "="*60)
        print("         WHATSAPP TROUBLESHOOTING MENU")
        print("="*60)
        print("1. Check Chrome profile status")
        print("2. Test browser connectivity")
        print("3. Show system information")
        print("4. Clear saved session (start fresh)")
        print("5. Full diagnostic report")
        print("6. Exit")
        print("="*60)
        
        choice = input("Enter your choice (1-6): ").strip()
        
        if choice == "1":
            check_chrome_profile()
        elif choice == "2":
            test_browser_connectivity()
        elif choice == "3":
            show_system_info()
        elif choice == "4":
            print("\n⚠️  This will delete your saved WhatsApp login!")
            confirm = input("Are you sure? (yes/no): ").strip().lower()
            if confirm in ["yes", "y"]:
                clear_chrome_profile()
            else:
                print("❌ Cancelled")
        elif choice == "5":
            print("\n🔍 Running full diagnostic...")
            print("\n" + "="*30 + " REPORT " + "="*30)
            show_system_info()
            check_chrome_profile()
            test_browser_connectivity()
            print("="*67)
            print("📋 Diagnostic complete!")
        elif choice == "6":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-6.")

if __name__ == "__main__":
    print("="*60)
    print("         WHATSAPP WEB TROUBLESHOOTER")
    print("="*60)
    print("This tool helps diagnose WhatsApp Web integration issues.")
    print("Run this if you're having problems with setup or messaging.")
    
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n👋 Troubleshooter cancelled by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        logging.error(f"Troubleshooter error: {e}")