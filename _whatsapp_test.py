#!/usr/bin/env python3
"""
WhatsApp Test Script
This script tests your WhatsApp Web setup by sending a test message.
Make sure you've run whatsapp_setup.py first!
"""

import time
import logging
from datetime import datetime
from whatsapp_sender import send_whatsapp_notification, WhatsAppSender

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def get_contact_input():
    """Get contact information from user"""
    print("="*60)
    print("         CONTACT SELECTION")
    print("="*60)
    print("You can specify the contact in two ways:")
    print("1. Contact Name: Enter the name exactly as it appears in WhatsApp")
    print("   Example: '<PERSON>' or 'Mom'")
    print("2. Phone Number: Enter with country code")
    print("   Example: '+1234567890' or '+44123456789'")
    print("="*60)
    
    while True:
        contact = input("\nEnter contact name or phone number: ").strip()
        if contact:
            return contact
        print("❌ Please enter a valid contact name or phone number")

def get_test_message():
    """Get or use default test message"""
    default_message = f"🤖 WhatsApp Test Message\n\nThis is a test from your Delaware Corporation Monitor!\n\nTime: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    print("\n" + "="*60)
    print("         MESSAGE CONTENT")
    print("="*60)
    print("Default message:")
    print("-" * 40)
    print(default_message)
    print("-" * 40)
    
    choice = input("\nUse default message? (y/n): ").strip().lower()
    if choice == 'n' or choice == 'no':
        print("\nEnter your custom message (press Enter twice when done):")
        lines = []
        while True:
            line = input()
            if line == "" and lines and lines[-1] == "":
                break
            lines.append(line)
        custom_message = "\n".join(lines[:-1])  # Remove the last empty line
        return custom_message if custom_message.strip() else default_message
    else:
        return default_message

def test_whatsapp_simple():
    """Simple test using the integrated function"""
    print("="*60)
    print("         WHATSAPP TEST (Simple Method)")
    print("="*60)
    
    contact = get_contact_input()
    message = get_test_message()
    
    print(f"\n📱 Sending test message to: {contact}")
    print("🔄 This may take a few moments...")
    
    try:
        success = send_whatsapp_notification(contact, message)
        if success:
            print("✅ Test message sent successfully!")
            return True
        else:
            print("❌ Failed to send test message")
            return False
    except Exception as e:
        print(f"❌ Error sending message: {e}")
        logging.error(f"Test message error: {e}")
        return False

def test_whatsapp_detailed():
    """Detailed test with step-by-step feedback"""
    print("="*60)
    print("         WHATSAPP TEST (Detailed Method)")
    print("="*60)
    
    contact = get_contact_input()
    message = get_test_message()
    
    whatsapp = None
    try:
        print("\n🚀 Initializing WhatsApp sender...")
        whatsapp = WhatsAppSender()
        
        print("🌐 Setting up browser...")
        whatsapp.setup_driver()
        
        print("🔐 Logging in to WhatsApp Web...")
        if not whatsapp.login_to_whatsapp():
            print("❌ Failed to login to WhatsApp Web")
            print("💡 Try running 'python whatsapp_setup.py' first")
            return False
        
        print("✅ Successfully logged in!")
        print(f"🔍 Looking for contact: {contact}")
        print(f"📤 Sending message...")
        
        success = whatsapp.send_message(contact, message)
        
        if success:
            print("✅ Test message sent successfully!")
            print(f"📱 Message sent to: {contact}")
            return True
        else:
            print("❌ Failed to send message")
            print("💡 Make sure the contact name/number is correct")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logging.error(f"Detailed test error: {e}")
        return False
    finally:
        if whatsapp:
            print("🧹 Cleaning up...")
            whatsapp.cleanup()

def main():
    """Main test function"""
    print("="*60)
    print("         WHATSAPP WEB TESTER")
    print("="*60)
    print("This script will test your WhatsApp Web setup.")
    print("Make sure you've run 'whatsapp_setup.py' first!")
    print("="*60)
    
    print("\nChoose test method:")
    print("1. Simple test (recommended)")
    print("2. Detailed test (with step-by-step feedback)")
    print("3. Exit")
    
    while True:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            print("\n🧪 Running simple test...")
            success = test_whatsapp_simple()
            break
        elif choice == "2":
            print("\n🧪 Running detailed test...")
            success = test_whatsapp_detailed()
            break
        elif choice == "3":
            print("👋 Goodbye!")
            return
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")
            continue
    
    print("\n" + "="*60)
    if success:
        print("🎉 TEST PASSED!")
        print("Your WhatsApp integration is working correctly.")
        print("You can now enable WhatsApp notifications in your monitor.")
    else:
        print("😞 TEST FAILED!")
        print("Troubleshooting tips:")
        print("1. Make sure you ran 'whatsapp_setup.py' first")
        print("2. Check that the contact name/number is exactly correct")
        print("3. Ensure WhatsApp Web is accessible in your browser")
    print("="*60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        logging.error(f"Test script error: {e}")
        print("Please check the logs and try again.")