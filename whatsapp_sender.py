import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging for WhatsApp sender
def setup_whatsapp_logger():
    """Set up a dedicated logger for WhatsApp sender"""
    logger = logging.getLogger('whatsapp_sender')
    logger.setLevel(logging.INFO)

    # Prevent adding handlers multiple times
    if not logger.handlers:
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # File handler
        file_handler = logging.FileHandler('whatsapp_sender.log')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    return logger

# Initialize the logger
whatsapp_logger = setup_whatsapp_logger()

class WhatsAppSender:

    def __init__(self):
        self.whatsapp_driver = None
        self.is_logged_in = False
        self.chromedriver_path = "C:\\Users\\<USER>\\tmp\\chromedriver\\chromedriver.exe"
        
    def start_browser(self):
        """Initialize Chrome WebDriver for WhatsApp Web"""
        chrome_options = Options()
        # Don't use headless for WhatsApp Web - it needs to display QR code for first login
        chrome_options.add_argument("--no-sandbox")
        # chrome_options.add_argument("--disable-dev-shm-usage")
        # chrome_options.add_argument("--disable-gpu")
        # chrome_options.add_argument("--window-size=1200,800")
        chrome_options.add_argument("--remote-debugging-port=9225")
        # Use a persistent user data directory to save login session
        # chrome_options.add_argument("--user-data-dir=./whatsapp_chrome_data")
        # chrome_options.add_argument("--profile-directory=WhatsAppProfile")

        try:
            # Clean up existing driver if it exists
            if self.whatsapp_driver:
                try:
                    self.whatsapp_driver.quit()
                except:
                    pass
                self.whatsapp_driver = None
                self.is_logged_in = False

            if self.chromedriver_path:
                # Use manually specified ChromeDriver path
                service = Service(self.chromedriver_path)
                self.whatsapp_driver = webdriver.Chrome(service=service, options=chrome_options)
                whatsapp_logger.info(f"WhatsApp WebDriver initialized with manual path: {self.chromedriver_path}")
            else:
                # Try to use ChromeDriver from PATH
                self.whatsapp_driver = webdriver.Chrome(options=chrome_options)
                whatsapp_logger.info("WhatsApp WebDriver initialized from PATH")

            self.whatsapp_driver.set_page_load_timeout(30)

            try:
                whatsapp_logger.info("Opening WhatsApp Web...")
                self.whatsapp_driver.get("https://web.whatsapp.com")

            except Exception as e:
                whatsapp_logger.error(f"Failed to login to WhatsApp: {e}")
                return False

            return True

        except Exception as e:
            whatsapp_logger.error(f"Failed to initialize WhatsApp WebDriver: {e}")
            whatsapp_logger.error("Make sure ChromeDriver is installed and accessible")
            raise

    def restart_browser(self):
        """Restart the WhatsApp WebDriver after a crash"""
        whatsapp_logger.warning("Attempting to restart WhatsApp WebDriver...")
        try:
            self.start_browser()
            whatsapp_logger.info("WhatsApp WebDriver restarted successfully")
            return True
        except Exception as e:
            whatsapp_logger.error(f"Failed to restart WhatsApp WebDriver: {e}")
            return False

    def is_driver_alive(self):
        """Check if the WhatsApp WebDriver is still alive and responsive"""
        try:
            if self.whatsapp_driver is None:
                return False
            # Try to get the current URL to test if driver is responsive
            _ = self.whatsapp_driver.current_url
            return True
        except Exception:
            return False

        
    def login_to_whatsapp(self):
        """Login to WhatsApp Web"""

        # Check if driver is alive
        if not self.is_driver_alive():
            whatsapp_logger.error("WhatsApp WebDriver is not alive, cannot login")
            return False

        # Check if already logged in by looking for the chat list
        try:
            # Look for chat list (logged in) or QR code (need to scan)
            WebDriverWait(self.whatsapp_driver, 10).until(
                lambda driver: driver.find_elements(By.CSS_SELECTOR, '[aria-label="Chat list"]') or
                                driver.find_elements(By.CSS_SELECTOR, '[aria-label="Scan me!"]')
            )

            # Check if we found the chat list (already logged in)
            if self.whatsapp_driver.find_elements(By.CSS_SELECTOR, '[aria-label="Chat list"]'):
                whatsapp_logger.info("Already logged in to WhatsApp Web")
                # self.whatsapp_driver.minimize_window()
                self.is_logged_in = True
                return True
            else:
                whatsapp_logger.warning("WhatsApp Web requires QR code scan - keeping browser open")
                whatsapp_logger.warning("Please scan the QR code to enable WhatsApp notifications")
                return False

        except TimeoutException:
            whatsapp_logger.error("QR code scan timeout or login failed")
            return False
                            
    
    def find_chat(self, contact_name_or_number):
        """Find and open a chat with the specified contact"""
        # Check if driver is alive
        if not self.is_driver_alive():
            whatsapp_logger.error("WhatsApp WebDriver is not alive, cannot find chat")
            return False

        try:
            # Click on search box
            search_box = WebDriverWait(self.whatsapp_driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '.lexical-rich-text-input'))
            )
            search_box.click()

            # clear search if available
            try:
                clear_button = WebDriverWait(self.whatsapp_driver, 3).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Cancel search"]'))
                )
                clear_button.click()
            except:
                pass

            # type contact name/number
            search_input = WebDriverWait(self.whatsapp_driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Search input textbox"]'))
            )
            search_input.send_keys(contact_name_or_number)
            time.sleep(2)  # Wait for search results

            # Click on the first search result
            first_result = WebDriverWait(self.whatsapp_driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '.matched-text'))
            )
            first_result.click()

            whatsapp_logger.info(f"Opened chat with {contact_name_or_number}")
            return True

        except Exception as e:
            whatsapp_logger.error(f"Failed to find chat with {contact_name_or_number}: {e}")
            return False
    
    def send_message(self, contact_name_or_number, message):
        """Send a message to the specified contact"""
        if not self.is_logged_in:
            whatsapp_logger.error("Not logged in to WhatsApp")
            return False

        # Check if driver is alive, restart if needed
        if not self.is_driver_alive():
            whatsapp_logger.warning("WhatsApp WebDriver appears to be dead, attempting restart...")
            if not self.restart_browser():
                whatsapp_logger.error("Failed to restart WhatsApp WebDriver")
                return False
            # Need to re-login after restart
            if not self.login_to_whatsapp():
                whatsapp_logger.error("Failed to re-login after restart")
                return False

        try:
            # Find and open the chat
            if not self.find_chat(contact_name_or_number):
                return False

            # Find the message input box
            message_box = WebDriverWait(self.whatsapp_driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Type a message"]'))
            )

            # Clear and type the message
            message_box.clear()
            message_box.send_keys(message)

            # Send the message
            message_box.send_keys(Keys.ENTER)

            whatsapp_logger.info(f"Message sent to {contact_name_or_number}: {message[:50]}...")
            time.sleep(2)  # Brief pause after sending
            return True

        except Exception as e:
            whatsapp_logger.error(f"Failed to send message: {e}")
            # Try to restart driver on errors
            if not self.is_driver_alive():
                whatsapp_logger.warning("Driver appears dead after error, attempting restart...")
                self.restart_browser()
            return False
    
    def cleanup(self):
        """Clean up resources but keep session for next use"""
        if self.whatsapp_driver:
            # Don't quit the driver to keep the session alive
            # Just minimize the window
            try:
                # self.whatsapp_driver.minimize_window()
                whatsapp_logger.info("WhatsApp browser minimized (keeping session alive)")
            except:
                pass
    
    def close(self):
        """Completely close the WhatsApp session"""
        if self.whatsapp_driver:
            self.whatsapp_driver.quit()
            whatsapp_logger.info("WhatsApp WebDriver closed")

# Integration function for your Delaware monitor
def send_whatsapp_notification(contact_name_or_number, message):
    """
    Send a WhatsApp message - to be called from your main monitor script
    """
    whatsapp = None
    try:
        whatsapp = WhatsAppSender()
        whatsapp.start_browser()
        whatsapp_logger.info("WhatsApp browser started")
        time.sleep(60)

        if whatsapp.login_to_whatsapp():
            return whatsapp.send_message(contact_name_or_number, message)
        else:
            while not whatsapp.login_to_whatsapp():
                time.sleep(120)  # Wait for 2 minutes before retrying
                whatsapp_logger.warning("Refreshing browser...")
                whatsapp.whatsapp_driver.refresh()

    except Exception as e:
        whatsapp_logger.error(f"WhatsApp notification failed: {e}")
        return False
    finally:
        if whatsapp:
            whatsapp.cleanup()  # Keep session alive for next use

# Example usage
if __name__ == "__main__":
    # Test the WhatsApp sender
    contact = "Paul Himself"  # Use contact name as it appears in WhatsApp
    # or use phone number like "+1234567890"
    
    message = "Automated message from Selenium"
    
    success = send_whatsapp_notification(contact, message)
    if success:
        print("WhatsApp message sent successfully!")
    else:
        print("Failed to send WhatsApp message")