import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('whatsapp_sender.log'),
        logging.StreamHandler()
    ]
)

class WhatsAppSender:

    def __init__(self):
        self.whatsapp_driver = None
        self.is_logged_in = False
        self.chromedriver_path = "C:\\Users\\<USER>\\tmp\\chromedriver\\chromedriver.exe"
        
    def start_browser(self):
        """Initialize Chrome WebDriver for WhatsApp Web"""
        chrome_options = Options()
        # Don't use headless for WhatsApp Web - it needs to display QR code for first login
        chrome_options.add_argument("--no-sandbox")
        # chrome_options.add_argument("--disable-dev-shm-usage")
        # chrome_options.add_argument("--disable-gpu")
        # chrome_options.add_argument("--window-size=1200,800")
        chrome_options.add_argument("--remote-debugging-port=9225")
        # Use a persistent user data directory to save login session
        # chrome_options.add_argument("--user-data-dir=./whatsapp_chrome_data")
        # chrome_options.add_argument("--profile-directory=WhatsAppProfile")
        
        try:
            if self.chromedriver_path:
                # Use manually specified ChromeDriver path
                service = Service(self.chromedriver_path)
                self.whatsapp_driver = webdriver.Chrome(service=service, options=chrome_options)
                logging.info(f"WhatsApp WebDriver initialized with manual path: {self.chromedriver_path}")
            else:
                # Try to use ChromeDriver from PATH
                self.whatsapp_driver = webdriver.Chrome(options=chrome_options)
                logging.info("WhatsApp WebDriver initialized from PATH")
            
            self.whatsapp_driver.set_page_load_timeout(30)
            
            try:
                logging.info("Opening WhatsApp Web...")
                self.whatsapp_driver.get("https://web.whatsapp.com")
    
            except Exception as e:
                logging.error(f"Failed to login to WhatsApp: {e}")
                return False
            
            return True
        
        except Exception as e:
            logging.error(f"Failed to initialize WhatsApp WebDriver: {e}")
            logging.error("Make sure ChromeDriver is installed and accessible")
            raise

        
    def login_to_whatsapp(self):
        """Login to WhatsApp Web"""
            
        # Check if already logged in by looking for the chat list
        try:
            # Look for chat list (logged in) or QR code (need to scan)
            WebDriverWait(self.whatsapp_driver, 10).until(
                lambda driver: driver.find_elements(By.CSS_SELECTOR, '[aria-label="Chat list"]') or
                                driver.find_elements(By.CSS_SELECTOR, '[aria-label="Scan me!"]')
            )
            
            # Check if we found the chat list (already logged in)
            if self.whatsapp_driver.find_elements(By.CSS_SELECTOR, '[aria-label="Chat list"]'):
                logging.info("Already logged in to WhatsApp Web")
                # self.whatsapp_driver.minimize_window()
                self.is_logged_in = True
                return True
            else:
                logging.warning("WhatsApp Web requires QR code scan - keeping browser open")
                logging.warning("Please scan the QR code to enable WhatsApp notifications")
                return False
            
        except TimeoutException:
            logging.error("QR code scan timeout or login failed")
            return False
                            
    
    def find_chat(self, contact_name_or_number):
        """Find and open a chat with the specified contact"""
        try:
            # Click on search box
            search_box = WebDriverWait(self.whatsapp_driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '.lexical-rich-text-input'))
            )
            search_box.click()
            
            # clear search if available
            try:
                clear_button = WebDriverWait(self.whatsapp_driver, 3).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Cancel search"]'))
                )
                clear_button.click()
            except:
                pass
            
            # type contact name/number
            search_input = WebDriverWait(self.whatsapp_driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Search input textbox"]'))
            )
            search_input.send_keys(contact_name_or_number)
            time.sleep(2)  # Wait for search results
            
            # Click on the first search result
            first_result = WebDriverWait(self.whatsapp_driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '.matched-text'))
            )
            first_result.click()
            
            logging.info(f"Opened chat with {contact_name_or_number}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to find chat with {contact_name_or_number}: {e}")
            return False
    
    def send_message(self, contact_name_or_number, message):
        """Send a message to the specified contact"""
        if not self.is_logged_in:
            logging.error("Not logged in to WhatsApp")
            return False
            
        try:
            # Find and open the chat
            if not self.find_chat(contact_name_or_number):
                return False
            
            # Find the message input box
            message_box = WebDriverWait(self.whatsapp_driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Type a message"]'))
            )
            
            # Clear and type the message
            message_box.clear()
            message_box.send_keys(message)
            
            # Send the message
            message_box.send_keys(Keys.ENTER)
            
            logging.info(f"Message sent to {contact_name_or_number}: {message[:50]}...")
            time.sleep(2)  # Brief pause after sending
            return True
            
        except Exception as e:
            logging.error(f"Failed to send message: {e}")
            return False
    
    def cleanup(self):
        """Clean up resources but keep session for next use"""
        if self.whatsapp_driver:
            # Don't quit the driver to keep the session alive
            # Just minimize the window
            try:
                # self.whatsapp_driver.minimize_window()
                logging.info("WhatsApp browser minimized (keeping session alive)")
            except:
                pass
    
    def close(self):
        """Completely close the WhatsApp session"""
        if self.whatsapp_driver:
            self.whatsapp_driver.quit()
            logging.info("WhatsApp WebDriver closed")

# Integration function for your Delaware monitor
def send_whatsapp_notification(contact_name_or_number, message):
    """
    Send a WhatsApp message - to be called from your main monitor script
    """
    whatsapp = None
    try:
        whatsapp = WhatsAppSender()
        whatsapp.start_browser()
        logging.info("WhatsApp browser started")
        time.sleep(60)
        
        if whatsapp.login_to_whatsapp():
            return whatsapp.send_message(contact_name_or_number, message)
        else:
            while not whatsapp.login_to_whatsapp():
                time.sleep(120)  # Wait for 2 minutes before retrying   
                logging.warning("Refreshing browser...")
                whatsapp.whatsapp_driver.refresh()
            
    except Exception as e:
        logging.error(f"WhatsApp notification failed: {e}")
        return False
    finally:
        if whatsapp:
            whatsapp.cleanup()  # Keep session alive for next use

# Example usage
if __name__ == "__main__":
    # Test the WhatsApp sender
    contact = "Paul Himself"  # Use contact name as it appears in WhatsApp
    # or use phone number like "+1234567890"
    
    message = "Automated message from Selenium"
    
    success = send_whatsapp_notification(contact, message)
    if success:
        print("WhatsApp message sent successfully!")
    else:
        print("Failed to send WhatsApp message")