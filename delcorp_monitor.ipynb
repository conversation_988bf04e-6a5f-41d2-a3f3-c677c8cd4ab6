# Import necessary libraries
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from winotify import Notification

# driver location for chrome
# PATH = "C:\data\chromedriver.exe"
PATH = "C:\\Users\\<USER>\\tmp\\chromedriver\\chromedriver.exe"
service = Service(PATH)
chrome_options = Options()
chrome_options.add_argument("--no-sandbox")
# chrome_options.add_argument("--disable-dev-shm-usage")
# chrome_options.add_argument("--disable-gpu")
chrome_options.add_argument("--remote-debugging-port=9222")
# Use a persistent user data directory to save login session
# chrome_options.add_argument("--user-data-dir=./whatsapp_chrome_data")
# chrome_options.add_argument("--profile-directory=WhatsAppProfile")


# Launch driver

driver = webdriver.Chrome(service=service, options=chrome_options)
# driver = webdriver.Chrome(service=service)
driver.get("https://web.whatsapp.com")

driver.refresh()

driver.close()

try:
    clear_button = WebDriverWait(driver, 3).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Cancel search"]'))
    )
    clear_button.click()
except:
    pass

def find_chat(contact_name_or_number):
    """Find and open a chat with the specified contact"""
    try:
        # Click on search box
        try:
            clear_button = WebDriverWait(driver, 3).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Cancel search"]'))
            )
            clear_button.click()
        except:
            pass
        # Click on search box
        search_box = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '.lexical-rich-text-input'))
        )
        search_box.click()
        
        # Clear and type contact name/number
        search_input = WebDriverWait(driver, 3).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Search input textbox"]'))
        )
        # search_input.clear()
            
        search_input.send_keys(contact_name_or_number)
        time.sleep(2)  # Wait for search results
        
        # Click on the first search result
        first_result = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '.matched-text'))
        )
        first_result.click()
        
        print(f"Opened chat with {contact_name_or_number}")
        return True
        
    except Exception as e:
        print(f"Failed to find chat with {contact_name_or_number}: {e}")
        return False


def send_message(contact_name_or_number, message):
    """Send a message to the specified contact"""
        
    try:
        # Find and open the chat
        if not find_chat(contact_name_or_number):
            return False
        
        # Find the message input box
        message_box = WebDriverWait(driver, 3).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, '[aria-label="Type a message"]'))
        )
        
        # Clear and type the message
        message_box.clear()
        message_box.send_keys(message)
        
        # Send the message
        message_box.send_keys(Keys.ENTER)
        
        print(f"Message sent to {contact_name_or_number}: {message[:50]}...")
        time.sleep(2)  # Brief pause after sending
        return True
        
    except Exception as e:
        print(f"Failed to send message: {e}")
        return False


send_message("Team Pedro", "You know what's crazy is I'm literally using the script to send this message right now")

is_logged_in = False

def login_to_whatsapp():
    """Login to WhatsApp Web"""
    try:
        logging.info("Opening WhatsApp Web...")
        driver.get("https://web.whatsapp.com")
        
        # Check if already logged in by looking for the chat list
        try:
            # Look for chat list (logged in) or QR code (need to scan)
            WebDriverWait(driver, 10).until(
                lambda driver: driver.find_elements(By.CSS_SELECTOR, '[aria-label="Chat list"]') or
                                driver.find_elements(By.CSS_SELECTOR, '[aria-label="Scan me!"]')
            )
            
            # Check if we found the chat list (already logged in)
            if driver.find_elements(By.CSS_SELECTOR, '[aria-label="Chat list"]'):
                print("Already logged in to WhatsApp Web")
                driver.minimize_window()
                is_logged_in = True
                return True
            else:
                print("WhatsApp Web requires QR code scan - keeping browser open")
                print("Please scan the QR code to enable WhatsApp notifications")
        except TimeoutException:
            print("QR code scan timeout or login failed")
            return False
                        
    except Exception as e:
        print(f"Failed to login to WhatsApp: {e}")
        return False
