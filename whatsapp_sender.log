2025-08-11 14:25:46,221 - INFO - WhatsApp WebDriver initialized with manual path: C:\Users\<USER>\tmp\chromedriver\chromedriver.exe
2025-08-11 14:25:46,223 - INFO - Opening WhatsApp Web...
2025-08-11 14:25:51,058 - INFO - <PERSON>s<PERSON>pp browser started
2025-08-11 14:26:51,117 - INFO - Already logged in to WhatsApp Web
2025-08-11 14:27:07,990 - ERROR - Failed to find chat with <PERSON> <PERSON>: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916c0ea]
	(No symbol) [0x0x7ff6891c2f56]
	(No symbol) [0x0x7ff6891c320c]
	(No symbol) [0x0x7ff6892165b7]
	(No symbol) [0x0x7ff6891eb17f]
	(No symbol) [0x0x7ff6892133d0]
	(No symbol) [0x0x7ff6891eaf13]
	(No symbol) [0x0x7ff6891b4151]
	(No symbol) [0x0x7ff6891b4ee3]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	GetHandleVerifier [0x0x7ff6893df944+115956]
	GetHandleVerifier [0x0x7ff6893dfaf9+116393]
	GetHandleVerifier [0x0x7ff6893c5f28+10968]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 14:27:08,012 - INFO - WhatsApp browser minimized (keeping session alive)
2025-08-11 14:27:39,888 - INFO - WhatsApp WebDriver initialized with manual path: C:\Users\<USER>\tmp\chromedriver\chromedriver.exe
2025-08-11 14:27:39,894 - INFO - Opening WhatsApp Web...
2025-08-11 14:27:43,687 - INFO - WhatsApp browser started
2025-08-11 14:28:43,705 - INFO - Already logged in to WhatsApp Web
2025-08-11 14:29:01,358 - ERROR - Failed to find chat with Paul Himself: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916c0ea]
	(No symbol) [0x0x7ff6891c2f56]
	(No symbol) [0x0x7ff6891c320c]
	(No symbol) [0x0x7ff6892165b7]
	(No symbol) [0x0x7ff6891eb17f]
	(No symbol) [0x0x7ff6892133d0]
	(No symbol) [0x0x7ff6891eaf13]
	(No symbol) [0x0x7ff6891b4151]
	(No symbol) [0x0x7ff6891b4ee3]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	GetHandleVerifier [0x0x7ff6893df944+115956]
	GetHandleVerifier [0x0x7ff6893dfaf9+116393]
	GetHandleVerifier [0x0x7ff6893c5f28+10968]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 14:29:01,362 - INFO - WhatsApp browser minimized (keeping session alive)
2025-08-11 14:40:05,781 - INFO - WhatsApp WebDriver initialized with manual path: C:\Users\<USER>\tmp\chromedriver\chromedriver.exe
2025-08-11 14:40:05,785 - INFO - Opening WhatsApp Web...
2025-08-11 14:40:09,397 - INFO - WhatsApp browser started
2025-08-11 14:41:09,429 - INFO - Already logged in to WhatsApp Web
2025-08-11 14:41:23,898 - INFO - Opened chat with Paul Himself
2025-08-11 14:41:24,318 - INFO - Message sent to Paul Himself: Automated message from Selenium...
2025-08-11 14:41:26,848 - INFO - WhatsApp browser minimized (keeping session alive)
2025-08-11 14:44:35,327 - INFO - Starting Delaware Corporation site monitor
2025-08-11 14:44:35,327 - INFO - Check interval: 10 minutes
2025-08-11 14:44:36,592 - INFO - WebDriver initialized successfully
2025-08-11 14:44:36,633 - INFO - Windows notification sent: Monitor Started
2025-08-11 14:44:36,633 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 14:44:46,720 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 14:44:46,721 - INFO - Next check in 10 minutes...
2025-08-11 14:54:46,721 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 14:54:51,068 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 14:54:51,069 - INFO - Next check in 10 minutes...
2025-08-11 15:04:51,070 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 15:04:55,250 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 15:04:55,250 - INFO - Next check in 10 minutes...
2025-08-11 15:14:55,252 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 15:14:59,341 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 15:14:59,341 - INFO - Next check in 10 minutes...
2025-08-11 15:24:59,343 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 15:25:03,507 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 15:25:03,507 - INFO - Next check in 10 minutes...
2025-08-11 15:32:17,080 - INFO - Monitor stopped by user
2025-08-11 15:32:17,092 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/ae85aa34a1876c4049ee3d251e1c71de
2025-08-11 15:32:29,021 - INFO - WhatsApp WebDriver initialized with manual path: C:\Users\<USER>\tmp\chromedriver\chromedriver.exe
2025-08-11 15:32:29,023 - INFO - Opening WhatsApp Web...
2025-08-11 15:32:32,757 - INFO - Starting Delaware Corporation site monitor
2025-08-11 15:32:32,758 - INFO - Check interval: 10 minutes
2025-08-11 15:32:33,966 - INFO - WebDriver initialized successfully
2025-08-11 15:32:33,991 - INFO - Windows notification sent: Monitor Started
2025-08-11 15:32:33,991 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 15:32:44,633 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 15:32:44,634 - INFO - Next check in 10 minutes...
2025-08-11 16:14:12,834 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 16:14:34,693 - ERROR - WebDriver error: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916c0ea]
	(No symbol) [0x0x7ff689158355]
	(No symbol) [0x0x7ff68917cefa]
	(No symbol) [0x0x7ff6891f2845]
	(No symbol) [0x0x7ff689212d02]
	(No symbol) [0x0x7ff6891eaf13]
	(No symbol) [0x0x7ff6891b4151]
	(No symbol) [0x0x7ff6891b4ee3]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	GetHandleVerifier [0x0x7ff6893df944+115956]
	GetHandleVerifier [0x0x7ff6893dfaf9+116393]
	GetHandleVerifier [0x0x7ff6893c5f28+10968]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 16:14:34,694 - INFO - Next check in 10 minutes...
2025-08-11 16:50:12,105 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 16:50:12,170 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 16:50:12,171 - INFO - Next check in 10 minutes...
2025-08-11 17:00:12,173 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:00:12,176 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 17:00:12,177 - INFO - Next check in 10 minutes...
2025-08-11 17:10:12,179 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:10:12,181 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 17:10:12,182 - INFO - Next check in 10 minutes...
2025-08-11 17:20:12,184 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:20:12,188 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 17:20:12,191 - INFO - Next check in 10 minutes...
2025-08-11 17:25:24,297 - INFO - Monitor stopped by user
2025-08-11 17:25:52,033 - INFO - WhatsApp WebDriver initialized with manual path: C:\Users\<USER>\tmp\chromedriver\chromedriver.exe
2025-08-11 17:25:52,037 - INFO - Opening WhatsApp Web...
2025-08-11 17:25:54,227 - INFO - Starting Delaware Corporation site monitor
2025-08-11 17:25:54,228 - INFO - Check interval: 5 minutes
2025-08-11 17:25:55,408 - INFO - WebDriver initialized successfully
2025-08-11 17:25:55,428 - INFO - Windows notification sent: Monitor Started
2025-08-11 17:25:55,429 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:26:04,503 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 17:26:04,504 - INFO - Next check in 5 minutes...
2025-08-11 17:31:04,505 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:31:08,751 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 17:31:08,752 - INFO - Next check in 5 minutes...
2025-08-11 17:36:08,753 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:36:14,512 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 17:36:14,513 - INFO - Next check in 5 minutes...
2025-08-11 17:41:14,516 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:41:18,867 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 17:41:18,867 - INFO - Next check in 5 minutes...
2025-08-11 17:46:18,870 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:46:23,084 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 17:46:23,085 - INFO - Next check in 5 minutes...
2025-08-11 17:51:23,086 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:51:27,403 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 17:51:27,404 - INFO - Next check in 5 minutes...
2025-08-11 17:56:27,406 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 17:56:31,634 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 17:56:31,635 - INFO - Next check in 5 minutes...
2025-08-11 18:01:31,638 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:01:35,928 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:01:35,929 - INFO - Next check in 5 minutes...
2025-08-11 18:06:35,930 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:06:40,180 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:06:40,181 - INFO - Next check in 5 minutes...
2025-08-11 18:11:40,181 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:11:44,543 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:11:44,544 - INFO - Next check in 5 minutes...
2025-08-11 18:16:44,545 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:16:48,669 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:16:48,670 - INFO - Next check in 5 minutes...
2025-08-11 18:21:48,673 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:21:52,935 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:21:52,937 - INFO - Next check in 5 minutes...
2025-08-11 18:26:52,940 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:26:57,439 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:26:57,440 - INFO - Next check in 5 minutes...
2025-08-11 18:31:57,442 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:32:01,516 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:32:01,516 - INFO - Next check in 5 minutes...
2025-08-11 18:37:01,518 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:37:05,737 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:37:05,738 - INFO - Next check in 5 minutes...
2025-08-11 18:42:05,740 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:42:09,937 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:42:09,938 - INFO - Next check in 5 minutes...
2025-08-11 18:47:09,940 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:47:14,113 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:47:14,114 - INFO - Next check in 5 minutes...
2025-08-11 18:52:14,117 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:52:18,309 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:52:18,310 - INFO - Next check in 5 minutes...
2025-08-11 18:57:18,311 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 18:57:22,609 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 18:57:22,610 - INFO - Next check in 5 minutes...
2025-08-11 19:02:22,612 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 19:02:26,925 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 19:02:26,925 - INFO - Next check in 5 minutes...
2025-08-11 19:07:26,927 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 19:07:31,155 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 19:07:31,155 - INFO - Next check in 5 minutes...
2025-08-11 19:12:31,156 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 19:12:35,439 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 19:12:35,440 - INFO - Next check in 5 minutes...
2025-08-11 19:17:35,440 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 19:17:39,668 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 19:17:39,669 - INFO - Next check in 5 minutes...
2025-08-11 19:22:39,670 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 19:22:43,690 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 19:22:43,690 - INFO - Next check in 5 minutes...
2025-08-11 19:29:37,691 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 19:29:42,484 - ERROR - WebDriver error: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916c0ea]
	(No symbol) [0x0x7ff689158355]
	(No symbol) [0x0x7ff68917cefa]
	(No symbol) [0x0x7ff6891f2845]
	(No symbol) [0x0x7ff689212d02]
	(No symbol) [0x0x7ff6891eaf13]
	(No symbol) [0x0x7ff6891b4151]
	(No symbol) [0x0x7ff6891b4ee3]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	GetHandleVerifier [0x0x7ff6893df944+115956]
	GetHandleVerifier [0x0x7ff6893dfaf9+116393]
	GetHandleVerifier [0x0x7ff6893c5f28+10968]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 19:29:42,486 - INFO - Next check in 5 minutes...
2025-08-11 19:34:42,488 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 19:34:42,498 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 19:34:42,501 - INFO - Next check in 5 minutes...
2025-08-11 19:50:54,002 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 19:50:54,111 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 19:50:54,132 - INFO - Next check in 5 minutes...
2025-08-11 19:51:25,519 - INFO - Monitor stopped by user
2025-08-11 20:10:33,788 - INFO - WhatsApp WebDriver initialized with manual path: C:\Users\<USER>\tmp\chromedriver\chromedriver.exe
2025-08-11 20:10:33,790 - INFO - Opening WhatsApp Web...
2025-08-11 20:10:38,081 - INFO - Starting Delaware Corporation site monitor
2025-08-11 20:10:38,082 - INFO - Check interval: 5 minutes
2025-08-11 20:10:39,233 - INFO - WebDriver initialized successfully
2025-08-11 20:10:39,268 - INFO - Windows notification sent: Monitor Started
2025-08-11 20:10:39,268 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 20:11:01,354 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 20:11:01,357 - INFO - Next check in 5 minutes...
2025-08-11 20:16:01,359 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 20:16:05,484 - WARNING - Site is DOWN - Emergency message detected
2025-08-11 20:16:05,485 - INFO - Next check in 5 minutes...
2025-08-11 20:31:19,153 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 20:31:42,118 - ERROR - WebDriver error: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916c0ea]
	(No symbol) [0x0x7ff689158355]
	(No symbol) [0x0x7ff68917cefa]
	(No symbol) [0x0x7ff6891f2845]
	(No symbol) [0x0x7ff689212d02]
	(No symbol) [0x0x7ff6891eaf13]
	(No symbol) [0x0x7ff6891b4151]
	(No symbol) [0x0x7ff6891b4ee3]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	GetHandleVerifier [0x0x7ff6893df944+115956]
	GetHandleVerifier [0x0x7ff6893dfaf9+116393]
	GetHandleVerifier [0x0x7ff6893c5f28+10968]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 20:31:42,119 - INFO - Next check in 5 minutes...
2025-08-11 20:36:42,120 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 20:36:42,123 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 20:36:42,124 - INFO - Next check in 5 minutes...
2025-08-11 20:41:42,125 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 20:41:42,129 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 20:41:42,131 - INFO - Next check in 5 minutes...
2025-08-11 21:43:32,903 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 21:43:33,305 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 21:43:33,325 - INFO - Next check in 5 minutes...
2025-08-11 22:34:40,238 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-11 22:34:40,370 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-11 22:34:40,371 - INFO - Next check in 5 minutes...
2025-08-12 02:43:36,023 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 02:43:36,643 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 02:43:37,090 - INFO - Next check in 5 minutes...
2025-08-12 03:16:07,785 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 03:16:08,077 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 03:16:08,097 - INFO - Next check in 5 minutes...
2025-08-12 03:16:34,246 - INFO - Monitor stopped by user
2025-08-12 03:16:55,478 - INFO - WhatsApp WebDriver initialized with manual path: C:\Users\<USER>\tmp\chromedriver\chromedriver.exe
2025-08-12 03:16:55,482 - INFO - Opening WhatsApp Web...
2025-08-12 03:16:57,274 - INFO - Starting Delaware Corporation site monitor
2025-08-12 03:16:57,276 - INFO - Check interval: 5 minutes
2025-08-12 03:16:58,479 - INFO - WebDriver initialized successfully
2025-08-12 03:16:58,509 - INFO - Windows notification sent: Monitor Started
2025-08-12 03:16:57,340 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 03:17:05,978 - WARNING - Site is DOWN - Emergency message detected
2025-08-12 03:17:05,978 - INFO - Next check in 5 minutes...
2025-08-12 03:22:05,980 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 03:22:10,086 - WARNING - Site is DOWN - Emergency message detected
2025-08-12 03:22:10,086 - INFO - Next check in 5 minutes...
2025-08-12 03:27:10,088 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 03:27:10,226 - ERROR - WebDriver error: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=139.0.7258.67); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916c0ea]
	(No symbol) [0x0x7ff689158355]
	(No symbol) [0x0x7ff68917cefa]
	(No symbol) [0x0x7ff6891f2845]
	(No symbol) [0x0x7ff689212d02]
	(No symbol) [0x0x7ff6891eaf13]
	(No symbol) [0x0x7ff6891b4151]
	(No symbol) [0x0x7ff6891b4ee3]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	GetHandleVerifier [0x0x7ff6893df944+115956]
	GetHandleVerifier [0x0x7ff6893dfaf9+116393]
	GetHandleVerifier [0x0x7ff6893c5f28+10968]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 03:27:10,227 - INFO - Next check in 5 minutes...
2025-08-12 03:54:59,520 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 03:54:59,574 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 03:54:59,574 - INFO - Next check in 5 minutes...
2025-08-12 03:59:59,577 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 03:59:59,584 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 03:59:59,587 - INFO - Next check in 5 minutes...
2025-08-12 06:24:04,059 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 06:24:04,179 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 06:24:04,202 - INFO - Next check in 5 minutes...
2025-08-12 06:29:04,229 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 06:29:04,232 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 06:29:04,233 - INFO - Next check in 5 minutes...
2025-08-12 07:31:34,405 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 07:31:34,622 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 07:31:34,679 - INFO - Next check in 5 minutes...
2025-08-12 08:28:27,343 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 08:28:27,434 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 08:28:27,454 - INFO - Next check in 5 minutes...
2025-08-12 08:33:27,477 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 08:33:27,485 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 08:33:27,492 - INFO - Next check in 5 minutes...
2025-08-12 10:23:38,455 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 10:23:39,123 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 10:23:39,249 - INFO - Next check in 5 minutes...
2025-08-12 14:04:51,059 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 14:04:51,195 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 14:04:51,363 - INFO - Next check in 5 minutes...
2025-08-12 20:23:44,729 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 20:23:45,087 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 20:23:45,354 - INFO - Next check in 5 minutes...
2025-08-12 20:28:45,358 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 20:28:45,363 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 20:28:45,364 - INFO - Next check in 5 minutes...
2025-08-12 21:29:44,302 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 21:29:44,453 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 21:29:44,514 - INFO - Next check in 5 minutes...
2025-08-12 22:34:45,377 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 22:34:45,641 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 22:34:45,694 - INFO - Next check in 5 minutes...
2025-08-12 22:39:45,814 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 22:39:45,827 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 22:39:45,837 - INFO - Next check in 5 minutes...
2025-08-12 22:44:45,857 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 22:44:45,860 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 22:44:45,861 - INFO - Next check in 5 minutes...
2025-08-12 22:49:45,880 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 22:49:46,026 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 22:49:46,054 - INFO - Next check in 5 minutes...
2025-08-12 22:54:46,055 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 22:54:46,057 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 22:54:46,057 - INFO - Next check in 5 minutes...
2025-08-12 22:59:46,058 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 22:59:46,060 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 22:59:46,060 - INFO - Next check in 5 minutes...
2025-08-12 23:04:46,061 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 23:04:46,185 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 23:04:46,253 - INFO - Next check in 5 minutes...
2025-08-12 23:09:46,254 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 23:09:46,257 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 23:09:46,258 - INFO - Next check in 5 minutes...
2025-08-12 23:14:46,260 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 23:14:46,267 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 23:14:46,268 - INFO - Next check in 5 minutes...
2025-08-12 23:19:46,318 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 23:19:46,399 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 23:19:46,416 - INFO - Next check in 5 minutes...
2025-08-12 23:24:46,432 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 23:24:46,437 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 23:24:46,440 - INFO - Next check in 5 minutes...
2025-08-12 23:48:59,759 - INFO - Checking site status: https://icis.corp.delaware.gov/ecorp2/
2025-08-12 23:49:00,122 - ERROR - WebDriver error: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x7ff6893d6b55+79621]
	GetHandleVerifier [0x0x7ff6893d6bb0+79712]
	(No symbol) [0x0x7ff68916bf1c]
	(No symbol) [0x0x7ff6891b327f]
	(No symbol) [0x0x7ff6891eb002]
	(No symbol) [0x0x7ff6891e5b23]
	(No symbol) [0x0x7ff6891e4be9]
	(No symbol) [0x0x7ff689136fd5]
	GetHandleVerifier [0x0x7ff68969686d+2962461]
	GetHandleVerifier [0x0x7ff689690b8d+2938685]
	GetHandleVerifier [0x0x7ff6896af74d+3064573]
	GetHandleVerifier [0x0x7ff6893f0c9e+186446]
	GetHandleVerifier [0x0x7ff6893f8a6f+218655]
	(No symbol) [0x0x7ff689135fd1]
	GetHandleVerifier [0x0x7ff6897ae458+4108296]
	BaseThreadInitThunk [0x0x7ff9b10de8d7+23]
	RtlUserThreadStart [0x0x7ff9b279c34c+44]

2025-08-12 23:49:00,125 - INFO - Next check in 5 minutes...
